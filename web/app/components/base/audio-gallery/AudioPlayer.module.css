.audioPlayer {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 8px;
  min-width: 240px;
  max-width: 420px;
  max-height: 40px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(16, 24, 40, 0.08);
  box-shadow: 0 1px 2px rgba(9, 9, 11, 0.05);
  gap: 8px;
}

.playButton {
  display: inline-flex;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #296DFF;
  color: white;
  border: none;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  transition: background-color 0.1s;
  flex-shrink: 0;
}

.playButton:hover {
  background-color: #3367d6;
}

.playButton:disabled {
  background-color: #bdbdbf;
}

.audioControls {
  flex-grow: 1;
  
}

.progressBarContainer {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.waveform {
  position: relative;
  display: flex;
  cursor: pointer;
  height: 24px;
  width: 100%;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
}

.progressBar {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.5;
  border-radius: 2px;
  flex: none;
  order: 55;
  flex-grow: 0;
  height: 100%;
  background-color: rgba(66, 133, 244, 0.3);
  pointer-events: none;
}

.timeDisplay {
  /* position: absolute; */
  color: #296DFF;
  border-radius: 2px;
  order: 0;
  height: 100%;
  width: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* .currentTime {
    position: absolute;
    bottom: calc(100% + 5px);
    transform: translateX(-50%);
    background-color: rgba(255,255,255,.8);
    padding: 2px 4px;
    border-radius:10px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.08);
} */

.duration {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 4px;
  border-radius: 10px;
}

.source_unavailable {
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: absolute;
  color: #bdbdbf;
}

.playButton svg path,
.playButton svg rect{
   fill:currentColor;
}

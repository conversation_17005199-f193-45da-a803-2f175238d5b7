// GENERATE BY script
// DON NOT EDIT IT MANUALLY

import * as React from 'react'
import s from './WxyyTextCn.module.css'
import cn from '@/utils/classnames'

const Icon = React.forwardRef<HTMLSpanElement, React.DetailedHTMLProps<React.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>>((
  { className, ...restProps },
  ref,
) => <span className={cn(s.wrapper, className)} {...restProps} ref={ref} />)

Icon.displayName = 'WxyyTextCn'

export default Icon

.stepHeader {
  position: sticky;
  top: 0;
  left: 0;
  padding: 42px 64px 12px 0;
  font-weight: 600;
  font-size: 18px;
  line-height: 28px;
}

.form {
  position: relative;
  padding: 12px 64px;
}

.dataSourceItem {
  @apply box-border relative grow shrink-0 flex items-center p-3 h-14 bg-white rounded-xl cursor-pointer;
  border: 0.5px solid #EAECF0;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #101828;
}

.dataSourceItem:hover {
  background-color: #f5f8ff;
  border: 0.5px solid #B2CCFF;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}

.dataSourceItem.active {
  background-color: #f5f8ff;
  border: 1.5px solid #528BFF;
  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);
}

.dataSourceItem.disabled {
  background-color: #f9fafb;
  border: 0.5px solid #EAECF0;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
  cursor: default;
}

.dataSourceItem.disabled:hover {
  background-color: #f9fafb;
  border: 0.5px solid #EAECF0;
  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
}

.comingTag {
  @apply flex justify-center items-center bg-white;
  position: absolute;
  right: 8px;
  top: -10px;
  padding: 1px 6px;
  height: 20px;
  border: 1px solid #E0EAFF;
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  color: #444CE7;
}

.datasetIcon {
  @apply flex mr-2 w-8 h-8 rounded-lg bg-center bg-no-repeat;
  background-color: #F5FAFF;
  background-image: url(../assets/file.svg);
  background-size: 16px;
  border: 0.5px solid #D1E9FF;
}

.dataSourceItem:active .datasetIcon,
.dataSourceItem:hover .datasetIcon {
  background-color: #F5F8FF;
  border: 0.5px solid #E0EAFF;
}

.datasetIcon.notion {
  background-image: url(../assets/notion.svg);
  background-size: 20px;
}

.datasetIcon.web {
  background-image: url(../assets/web.svg);
}

.submitButton {
  width: 120px;
}

.dividerLine {
  margin: 32px 0;
  max-width: 640px;
  height: 1px;
  background-color: #eaecf0;
}

.notionConnectionTip {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 24px;
  width: 640px;
  background: #F9FAFB;
  border-radius: 16px;
}

.notionIcon {
  display: flex;
  padding: 12px;
  width: 48px;
  height: 48px;
  background: #fff center no-repeat url(../assets/notion.svg);
  background-size: 24px;
  border: 0.5px solid #EAECF5;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  border-radius: 12px;
}

.notionConnectionTip .title {
  position: relative;
  margin: 24px 0 4px;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #374151;
}

.notionConnectionTip .title::after {
  content: '';
  position: absolute;
  top: -6px;
  right: -12px;
  width: 16px;
  height: 16px;
  background: center no-repeat url(../assets/Icon-3-dots.svg);
  background-size: contain;
}

.notionConnectionTip .tip {
  margin-bottom: 20px;
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 18px;
  color: #6B7280;
}
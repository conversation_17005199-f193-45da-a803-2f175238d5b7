import { CodeGroup } from '../code.tsx'
import { Row, Col, Properties, Property, Heading, SubProperty, Paragraph } from '../md.tsx'

# Completion App API

The text generation application offers non-session support and is ideal for translation, article writing, summarization AI, and more.

<div>
  ### Base URL
  <CodeGroup title="Code" targetCode={props.appDetail.api_base_url}>
    ```javascript
    ```
  </CodeGroup>

  ### Authentication

  The Service API uses `API-Key` authentication.
  <i>**Strongly recommend storing your API Key on the server-side, not shared or stored on the client-side, to avoid possible API-Key leakage that can lead to serious consequences.**</i>

  For all API requests, include your API Key in the `Authorization` HTTP Header, as shown below:

  <CodeGroup title="Code">
    ```javascript
      Authorization: Bearer {API_KEY}

    ```
  </CodeGroup>
</div>

---

<Heading
  url='/completion-messages'
  method='POST'
  title='Create Completion Message'
  name='#Create-Completion-Message'
/>
<Row>
  <Col>
    Send a request to the text generation application.

    ### Request Body

    <Properties>
      
      <Property name='inputs' type='object' key='inputs'>
          Allows the entry of various variable values defined by the App.
          The `inputs` parameter contains multiple key/value pairs, with each key corresponding to a specific variable and each value being the specific value for that variable.
          The text generation application requires at least one key/value pair to be inputted.
          - `query` (string) Required
            The input text, the content to be processed.
      </Property>
      <Property name='response_mode' type='string' key='response_mode'>
        The mode of response return, supporting:
        - `streaming` Streaming mode (recommended), implements a typewriter-like output through SSE ([Server-Sent Events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events)).
        - `blocking` Blocking mode, returns result after execution is complete. (Requests may be interrupted if the process is long)
        <i>Due to Cloudflare restrictions, the request will be interrupted without a return after 100 seconds.</i>
      </Property>
      <Property name='user' type='string' key='user'>
          User identifier, used to define the identity of the end-user for retrieval and statistics.
          Should be uniquely defined by the developer within the application.
      </Property>
      <Property name='files' type='array[object]' key='files'>
          File list, suitable for inputting files (images) combined with text understanding and answering questions, available only when the model supports Vision capability.
          - `type` (string) Supported type: `image` (currently only supports image type)
          - `transfer_method` (string) Transfer method, `remote_url` for image URL / `local_file` for file upload
          - `url` (string) Image URL (when the transfer method is `remote_url`)
          - `upload_file_id` (string) Uploaded file ID, which must be obtained by uploading through the File Upload API in advance (when the transfer method is `local_file`)
      </Property>
    </Properties>

    ### Response
    When `response_mode` is `blocking`, return a CompletionResponse object.
    When `response_mode` is `streaming`, return a ChunkCompletionResponse stream.

    ### ChatCompletionResponse
    Returns the complete App result, `Content-Type` is `application/json`.
    - `message_id` (string) Unique message ID
    - `mode` (string) App mode, fixed as `chat`
    - `answer` (string) Complete response content
    - `metadata` (object) Metadata
      - `usage` (Usage) Model usage information
      - `retriever_resources` (array[RetrieverResource]) Citation and Attribution List
    - `created_at` (int) Message creation timestamp, e.g., 1705395332

    ### ChunkChatCompletionResponse
    Returns the stream chunks outputted by the App, `Content-Type` is `text/event-stream`.
    Each streaming chunk starts with `data:`, separated by two newline characters `\n\n`, as shown below:
    <CodeGroup>
    ```streaming {{ title: 'Response' }}
    data: {"event": "message", "task_id": "900bbd43-dc0b-4383-a372-aa6e6c414227", "id": "663c5084-a254-4040-8ad3-51f2a3c1a77c", "answer": "Hi", "created_at": 1705398420}\n\n
    ```
    </CodeGroup>
    The structure of the streaming chunks varies depending on the `event`:
    - `event: message` LLM returns text chunk event, i.e., the complete text is output in a chunked fashion.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `answer` (string) LLM returned text chunk content
      - `created_at` (int) Creation timestamp, e.g., 1705395332
    - `event: message_end` Message end event, receiving this event means streaming has ended.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `metadata` (object) Metadata
        - `usage` (Usage) Model usage information
        - `retriever_resources` (array[RetrieverResource]) Citation and Attribution List
    - `event: tts_message` TTS audio stream event, that is, speech synthesis output. The content is an audio block in Mp3 format, encoded as a base64 string. When playing, simply decode the base64 and feed it into the player. (This message is available only when auto-play is enabled)
      - `task_id` (string) Task ID, used for request tracking and the stop response interface below
      - `message_id` (string) Unique message ID
      - `audio` (string) The audio after speech synthesis, encoded in base64 text content, when playing, simply decode the base64 and feed it into the player
      - `created_at` (int) Creation timestamp, e.g.: 1705395332
    - `event: tts_message_end` TTS audio stream end event, receiving this event indicates the end of the audio stream.
      - `task_id` (string) Task ID, used for request tracking and the stop response interface below
      - `message_id` (string) Unique message ID
      - `audio` (string) The end event has no audio, so this is an empty string
      - `created_at` (int) Creation timestamp, e.g.: 1705395332
    - `event: message_replace` Message content replacement event.
      When output content moderation is enabled, if the content is flagged, then the message content will be replaced with a preset reply through this event.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `answer` (string) Replacement content (directly replaces all LLM reply text)
      - `created_at` (int) Creation timestamp, e.g., 1705395332
    - `event: error`
      Exceptions that occur during the streaming process will be output in the form of stream events, and reception of an error event will end the stream.
      - `task_id` (string) Task ID, used for request tracking and the below Stop Generate API
      - `message_id` (string) Unique message ID
      - `status` (int) HTTP status code
      - `code` (string) Error code
      - `message` (string) Error message
    - `event: ping` Ping event every 10 seconds to keep the connection alive.

    ### Errors
    - 404, Conversation does not exists
    - 400, `invalid_param`, abnormal parameter input
    - 400, `app_unavailable`, App configuration unavailable
    - 400, `provider_not_initialize`, no available model credential configuration
    - 400, `provider_quota_exceeded`, model invocation quota insufficient
    - 400, `model_currently_not_support`, current model unavailable
    - 400, `completion_request_error`, text generation failed
    - 500, internal server error

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/completion-messages" targetCode={`curl -X POST '${props.appDetail.api_base_url}/completion-messages' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n    "inputs": {"query": "Hello, world!"},\n    "response_mode": "streaming",\n    "user": "abc-123"\n}'\n`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/completion-messages' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "inputs": {
          "query": "Hello, world!"
        },
        "response_mode": "streaming",
        "user": "abc-123"
    }'
    ```

    </CodeGroup>
    ### Blocking Mode
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "event": "message",
      "message_id": "9da23599-e713-473b-982c-4328d4f5c78a",
      "mode": "completion",
      "answer": "Hello World!...",
      "metadata": {
          "usage": {
              "prompt_tokens": 1033,
              "prompt_unit_price": "0.001",
              "prompt_price_unit": "0.001",
              "prompt_price": "0.0010330",
              "completion_tokens": 128,
              "completion_unit_price": "0.002",
              "completion_price_unit": "0.001",
              "completion_price": "0.0002560",
              "total_tokens": 1161,
              "total_price": "0.0012890",
              "currency": "USD",
              "latency": 0.7682376249867957
          }
      },
      "created_at": 1705407629
  }
    ```
    </CodeGroup>
    ### Streaming Mode
    <CodeGroup title="Response">
    ```streaming {{ title: 'Response' }}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "answer": " I", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "answer": "'m", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "answer": " glad", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "answer": " to", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "answer": " meet", "created_at": 1679586595}
      data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "answer": " you", "created_at": 1679586595}
      data: {"event": "message_end", "id": "5e52ce04-874b-4d27-9045-b3bc80def685", "metadata": {"usage": {"prompt_tokens": 1033, "prompt_unit_price": "0.001", "prompt_price_unit": "0.001", "prompt_price": "0.0010330", "completion_tokens": 135, "completion_unit_price": "0.002", "completion_price_unit": "0.001", "completion_price": "0.0002700", "total_tokens": 1168, "total_price": "0.0013030", "currency": "USD", "latency": 1.381760165997548}}}
      data: {"event": "tts_message", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": "qqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq"}
      data: {"event": "tts_message_end", "conversation_id": "23dd85f3-1a41-4ea0-b7a9-062734ccfaf9", "message_id": "a8bdc41c-13b2-4c18-bfd9-054b9803038c", "created_at": 1721205487, "task_id": "3bf8a0bb-e73b-4690-9e66-4e429bad8ee7", "audio": ""}
    ```
    </CodeGroup>
  </Col>
</Row>

---
<Heading
  url='/files/upload'
  method='POST'
  title='File Upload'
  name='#file-upload'
/>
<Row>
  <Col>
  Upload a file (currently only images are supported) for use when sending messages, enabling multimodal understanding of images and text.
  Supports png, jpg, jpeg, webp, gif formats.
  <i>Uploaded files are for use by the current end-user only.</i>

  ### Request Body
  This interface requires a `multipart/form-data` request.
  - `file` (File) Required
    The file to be uploaded.
  - `user` (string) Required
    User identifier, defined by the developer's rules, must be unique within the application.

  ### Response
  After a successful upload, the server will return the file's ID and related information.
  - `id` (uuid) ID
  - `name` (string) File name
  - `size` (int) File size (bytes)
  - `extension` (string) File extension
  - `mime_type` (string) File mime-type
  - `created_by` (uuid) End-user ID
  - `created_at` (timestamp) Creation timestamp, e.g., 1705395332

  ### Errors
  - 400, `no_file_uploaded`, a file must be provided
  - 400, `too_many_files`, currently only one file is accepted
  - 400, `unsupported_preview`, the file does not support preview
  - 400, `unsupported_estimate`, the file does not support estimation
  - 413, `file_too_large`, the file is too large
  - 415, `unsupported_file_type`, unsupported extension, currently only document files are accepted
  - 503, `s3_connection_failed`, unable to connect to S3 service
  - 503, `s3_permission_denied`, no permission to upload files to S3
  - 503, `s3_file_too_large`, file exceeds S3 size limit
  - 500, internal server error


  </Col>
  <Col sticky>
  ### Request Example
  <CodeGroup title="Request" tag="POST" label="/files/upload" targetCode={`curl -X POST '${props.appDetail.api_base_url}/files/upload' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'file=@localfile;type=image/[png|jpeg|jpg|webp|gif] \\\n--form 'user=abc-123'`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/files/upload' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'file=@"/path/to/file"'
    ```

    </CodeGroup>


  ### Response Example
  <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "72fa9618-8f89-4a37-9b33-7e1178a24a67",
      "name": "example.png",
      "size": 1024,
      "extension": "png",
      "mime_type": "image/png",
      "created_by": "6ad1ab0a-73ff-4ac1-b9e4-cdb312f71f13",
      "created_at": 1577836800,
    }
  ```
  </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/completion-messages/:task_id/stop'
  method='POST'
  title='Stop Generate'
  name='#stop-generatebacks'
/>
<Row>
  <Col>
  Only supported in streaming mode.
  ### Path
  - `task_id` (string) Task ID, can be obtained from the streaming chunk return
  Request Body
  - `user` (string) Required
    User identifier, used to define the identity of the end-user, must be consistent with the user passed in the send message interface.
  ### Response
  - `result` (string) Always returns "success"
  </Col>
  <Col sticky>
  ### Request Example
  <CodeGroup title="Request" tag="POST" label="/completion-messages/:task_id/stop" targetCode={`curl -X POST '${props.appDetail.api_base_url}/completion-messages/:task_id/stop' \\\n-H 'Authorization: Bearer {api_key}' \\\n-H 'Content-Type: application/json' \\\n--data-raw '{ "user": "abc-123"}'`}>
    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/completion-messages/:task_id/stop' \
    -H 'Authorization: Bearer {api_key}' \
    -H 'Content-Type: application/json' \
    --data-raw '{
        "user": "abc-123"
    }'
    ```
    </CodeGroup>

    ### Response Example
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/messages/:message_id/feedbacks'
  method='POST'
  title='Message Feedback'
  name='#feedbacks'
/>
<Row>
  <Col>
    End-users can provide feedback messages, facilitating application developers to optimize expected outputs.

    ### Path
    <Properties>
      <Property name='message_id' type='string' key='message_id'>
       Message ID
      </Property>
    </Properties>

    ### Request Body

    <Properties>
      <Property name='rating' type='string' key='rating'>
        Upvote as `like`, downvote as `dislike`, revoke upvote as `null`
      </Property>
      <Property name='user' type='string' key='user'>
        User identifier, defined by the developer's rules, must be unique within the application.
      </Property>
      <Property name='content' type='string' key='content'>
        The specific content of message feedback.
      </Property>
    </Properties>

    ### Response
    - `result` (string) Always returns "success"
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/messages/:message_id/feedbacks" targetCode={`curl -X POST '${props.appDetail.api_base_url}/messages/:message_id/feedbacks \\\n --header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n    "rating": "like",\n    "user": "abc-123",\n    "content": "message feedback information"\n}'`}>

    ```bash {{ title: 'cURL' }}
    curl -X POST '${props.appDetail.api_base_url}/messages/:message_id/feedbacks' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "rating": "like",
        "user": "abc-123",
        "content": "message feedback information"
    }'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/text-to-audio'
  method='POST'
  title='Text to Audio'
  name='#audio'
/>
<Row>
  <Col>
    Text to speech.

    ### Request Body

    <Properties>
      <Property name='message_id' type='str' key='text'>
        For text messages generated by Dify, simply pass the generated message-id directly. The backend will use the message-id to look up the corresponding content and synthesize the voice information directly. If both message_id and text are provided simultaneously, the message_id is given priority.
      </Property>
      <Property name='text' type='str' key='text'>
        Speech generated content.
      </Property>
      <Property name='user' type='string' key='user'>
        The user identifier, defined by the developer, must ensure uniqueness within the app.
      </Property>
    </Properties>
  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/text-to-audio" targetCode={`curl -o text-to-audio.mp3 -X POST '${props.appDetail.api_base_url}/text-to-audio' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n    "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290",\n    "text": "Hello Dify",\n    "user": "abc-123"\n}'`}>

    ```bash {{ title: 'cURL' }}
    curl -o text-to-audio.mp3 -X POST '${props.appDetail.api_base_url}/text-to-audio' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290",
        "text": "Hello Dify",
        "user": "abc-123"
    }'
    ```
    
    </CodeGroup>

    <CodeGroup title="headers">
    ```json {{ title: 'headers' }}
    {
      "Content-Type": "audio/wav"
    }
    ```
    </CodeGroup>
  </Col>
</Row>
---

<Heading
  url='/info'
  method='GET'
  title='Get Application Basic Information'
  name='#info'
/>
<Row>
  <Col>
  Used to get basic information about this application

  ### Response
  - `name` (string) application name
  - `description` (string) application description
  - `tags` (array[string]) application tags
  </Col>
  <Col>
    <CodeGroup title="Request" tag="GET" label="/info" targetCode={`curl -X GET '${props.appDetail.api_base_url}/info' \\\n-H 'Authorization: Bearer {api_key}'`}>
      ```bash {{ title: 'cURL' }}
      curl -X GET '${props.appDetail.api_base_url}/info' \
      -H 'Authorization: Bearer {api_key}'
      ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "name": "My App",
      "description": "This is my app.",
      "tags": [
        "tag1",
        "tag2"
      ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

---

<Heading
  url='/parameters'
  method='GET'
  title='Get Application Parameters Information'
  name='#parameters'
/>
<Row>
  <Col>
    Used at the start of entering the page to obtain information such as features, input parameter names, types, and default values.

    ### Response
    - `opening_statement` (string) Opening statement
    - `suggested_questions` (array[string]) List of suggested questions for the opening
    - `suggested_questions_after_answer` (object) Suggest questions after enabling the answer.
      - `enabled` (bool) Whether it is enabled
    - `speech_to_text` (object) Speech to text
      - `enabled` (bool) Whether it is enabled
    - `retriever_resource` (object) Citation and Attribution
      - `enabled` (bool) Whether it is enabled
    - `annotation_reply` (object) Annotation reply
      - `enabled` (bool) Whether it is enabled
    - `user_input_form` (array[object]) User input form configuration
      - `text-input` (object) Text input control
        - `label` (string) Variable display label name
        - `variable` (string) Variable ID
        - `required` (bool) Whether it is required
        - `default` (string) Default value
      - `paragraph` (object) Paragraph text input control
        - `label` (string) Variable display label name
        - `variable` (string) Variable ID
        - `required` (bool) Whether it is required
        - `default` (string) Default value
      - `select` (object) Dropdown control
        - `label` (string) Variable display label name
        - `variable` (string) Variable ID
        - `required` (bool) Whether it is required
        - `default` (string) Default value
        - `options` (array[string]) Option values
    - `file_upload` (object) File upload configuration
      - `image` (object) Image settings
        Currently only supports image types: `png`, `jpg`, `jpeg`, `webp`, `gif`
        - `enabled` (bool) Whether it is enabled
        - `number_limits` (int) Image number limit, default is 3
        - `transfer_methods` (array[string]) List of transfer methods, remote_url, local_file, must choose one
    - `system_parameters` (object) System parameters
      - `file_size_limit` (int) Document upload size limit (MB)
      - `image_file_size_limit` (int) Image file upload size limit (MB)
      - `audio_file_size_limit` (int) Audio file upload size limit (MB)
      - `video_file_size_limit` (int) Video file upload size limit (MB)

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="GET" label="/parameters" targetCode={` curl -X GET '${props.appDetail.api_base_url}/parameters'`}>

    ```bash {{ title: 'cURL' }}
    curl -X GET '${props.appDetail.api_base_url}/parameters' \
    --header 'Authorization: Bearer {api_key}'
    ```

    </CodeGroup>

    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "opening_statement": "Hello!",
      "suggested_questions_after_answer": {
          "enabled": true
      },
      "speech_to_text": {
          "enabled": true
      },
      "retriever_resource": {
          "enabled": true
      },
      "annotation_reply": {
          "enabled": true
      },
      "user_input_form": [
          {
              "paragraph": {
                  "label": "Query",
                  "variable": "query",
                  "required": true,
                  "default": ""
              }
          }
      ],
      "file_upload": {
          "image": {
              "enabled": false,
              "number_limits": 3,
              "detail": "high",
              "transfer_methods": [
                  "remote_url",
                  "local_file"
              ]
          }
      },
      "system_parameters": {
          "file_size_limit": 15,
          "image_file_size_limit": 10,
          "audio_file_size_limit": 50,
          "video_file_size_limit": 100
      }
    }
    ```
    </CodeGroup>
  </Col>
</Row>

'use client'
import type { <PERSON> } from 'react'
import React from 'react'
import cn from '@/utils/classnames'

type Props = {
  className?: string
  title: string
  children: JSX.Element
}

const Field: FC<Props> = ({
  className,
  title,
  children,
}) => {
  return (
    <div className={cn(className)}>
      <div className='text-text-secondary system-sm-semibold leading-8'>{title}</div>
      <div>{children}</div>
    </div>
  )
}
export default React.memo(Field)

import { CodeGroup } from '@/app/components/develop/code.tsx'
import { Row, Col, Properties, Property, Heading, SubProperty, PropertyInstruction, Paragraph } from '@/app/components/develop/md.tsx'

# 知识库 API

<div>
  ### 鉴权

  Dify Service API 使用 `API-Key` 进行鉴权。

  建议开发者把 `API-Key` 放在后端存储，而非分享或者放在客户端存储，以免 `API-Key` 泄露，导致财产损失。

  所有 API 请求都应在 **`Authorization`** HTTP Header 中包含您的 `API-Key`，如下所示：

  <CodeGroup title="Code">
    ```javascript
      Authorization: Bearer {API_KEY}

    ```
  </CodeGroup>
</div>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/document/create-by-text'
  method='POST'
  title='通过文本创建文档'
  name='#create-by-text'
/>
<Row>
  <Col>
    此接口基于已存在知识库，在此知识库的基础上通过文本创建新的文档

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        文档名称
      </Property>
      <Property name='text' type='string' key='text'>
        文档内容
      </Property>
      <Property name='doc_type' type='string' key='doc_type'>
        文档类型（选填）
          - <code>book</code> 图书 Book
          - <code>web_page</code> 网页 Web page
          - <code>paper</code> 学术论文/文章 Academic paper/article 
          - <code>social_media_post</code> 社交媒体帖子 Social media post
          - <code>wikipedia_entry</code> 维基百科条目 Wikipedia entry
          - <code>personal_document</code> 个人文档 Personal document
          - <code>business_document</code> 商业文档 Business document
          - <code>im_chat_log</code> 即时通讯记录 Chat log
          - <code>synced_from_notion</code> Notion同步文档 Notion document
          - <code>synced_from_github</code> GitHub同步文档 GitHub document
          - <code>others</code> 其他文档类型 Other document types
      </Property>
      <Property name='doc_metadata' type='object' key='doc_metadata'>
      
        文档元数据（如提供文档类型则必填）。字段因文档类型而异：
          
          针对图书 For <code>book</code>:
          - <code>title</code> 书名 Book title 
          - <code>language</code> 图书语言 Book language
          - <code>author</code> 作者 Book author
          - <code>publisher</code> 出版社 Publisher name
          - <code>publication_date</code> 出版日期 Publication date
          - <code>isbn</code> ISBN号码 ISBN number
          - <code>category</code> 图书分类 Book category

          针对网页 For <code>web_page</code>:
          - <code>title</code> 页面标题 Page title
          - <code>url</code> 页面网址 Page URL
          - <code>language</code> 页面语言 Page language
          - <code>publish_date</code> 发布日期 Publish date
          - <code>author/publisher</code> 作者/发布者 Author or publisher
          - <code>topic/keywords</code> 主题/关键词 Topic or keywords
          - <code>description</code> 页面描述 Page description

          请查看 [api/services/dataset_service.py](https://github.com/langgenius/dify/blob/main/api/services/dataset_service.py#L475) 了解各文档类型所需字段的详细信息。

          针对"其他"类型文档，接受任何有效的JSON对象
      </Property>
      <Property name='indexing_technique' type='string' key='indexing_technique'>
        索引方式
          - <code>high_quality</code> 高质量：使用  embedding 模型进行嵌入，构建为向量数据库索引
          - <code>economy</code> 经济：使用 keyword table index 的倒排索引进行构建
      </Property>
      <Property name='doc_form' type='string' key='doc_form'>
        索引内容的形式
          - <code>text_model</code> text 文档直接 embedding，经济模式默认为该模式
          - <code>hierarchical_model</code> parent-child 模式
          - <code>qa_model</code> Q&A 模式：为分片文档生成 Q&A 对，然后对问题进行 embedding
      </Property>
      <Property name='doc_language' type='string' key='doc_language'>
        在 Q&A 模式下，指定文档的语言，例如：<code>English</code>、<code>Chinese</code>
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        处理规则
          - <code>mode</code> (string) 清洗、分段模式 ，automatic 自动 / custom 自定义
          - <code>rules</code> (object) 自定义规则（自动模式下，该字段为空）
            - <code>pre_processing_rules</code> (array[object]) 预处理规则
              - <code>id</code> (string) 预处理规则的唯一标识符
                - 枚举：
                  - <code>remove_extra_spaces</code> 替换连续空格、换行符、制表符
                  - <code>remove_urls_emails</code> 删除 URL、电子邮件地址
              - <code>enabled</code> (bool) 是否选中该规则，不传入文档 ID 时代表默认值
            - <code>segmentation</code> (object) 分段规则
              - <code>separator</code> 自定义分段标识符，目前仅允许设置一个分隔符。默认为 <code>\n</code>
              - <code>max_tokens</code> 最大长度（token）默认为 1000
            - <code>parent_mode</code> 父分段的召回模式 <code>full-doc</code> 全文召回 / <code>paragraph</code> 段落召回
            - <code>subchunk_segmentation</code> (object) 子分段规则
              - <code>separator</code> 分段标识符，目前仅允许设置一个分隔符。默认为 <code>***</code>
              - <code>max_tokens</code> 最大长度 (token) 需要校验小于父级的长度
              - <code>chunk_overlap</code> 分段重叠指的是在对数据进行分段时，段与段之间存在一定的重叠部分（选填）
      </Property>
      <PropertyInstruction>当知识库未设置任何参数的时候，首次上传需要提供以下参数，未提供则使用默认选项：</PropertyInstruction>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        检索模式
          - <code>search_method</code> (string) 检索方法
            - <code>hybrid_search</code> 混合检索
            - <code>semantic_search</code> 语义检索
            - <code>full_text_search</code> 全文检索
          - <code>reranking_enable</code> (bool) 是否开启rerank
          - <code>reranking_model</code> (object) Rerank 模型配置
            - <code>reranking_provider_name</code> (string) Rerank 模型的提供商
            - <code>reranking_model_name</code> (string) Rerank 模型的名称
          - <code>top_k</code> (int) 召回条数
          - <code>score_threshold_enabled</code> (bool)是否开启召回分数限制
          - <code>score_threshold</code> (float) 召回分数限制
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        Embedding 模型名称
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        Embedding 模型供应商
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/document/create-by-text"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-text' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "text","text": "text","indexing_technique": "high_quality","process_rule": {"mode": "automatic"}}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-text' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "text",
        "text": "text",
        "indexing_technique": "high_quality",
        "process_rule": {
            "mode": "automatic"
        }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
            "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "text.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695690280,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/document/create-by-file'
  method='POST'
  title='通过文件创建文档 '
  name='#create-by-file'
/>
<Row>
  <Col>
    此接口基于已存在知识库，在此知识库的基础上通过文件创建新的文档

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='data' type='multipart/form-data json string' key='data'>
        - <code>original_document_id</code> 源文档 ID（选填）
          - 用于重新上传文档或修改文档清洗、分段配置，缺失的信息从源文档复制
          - 源文档不可为归档的文档
          - 当传入 <code>original_document_id</code> 时，代表文档进行更新操作，<code>process_rule</code> 为可填项目，不填默认使用源文档的分段方式
          - 未传入 <code>original_document_id</code> 时，代表文档进行新增操作，<code>process_rule</code> 为必填

        - <code>indexing_technique</code> 索引方式
          - <code>high_quality</code> 高质量：使用  embedding 模型进行嵌入，构建为向量数据库索引
          - <code>economy</code> 经济：使用 keyword table index 的倒排索引进行构建

        - <code>doc_form</code> 索引内容的形式
          - <code>text_model</code> text 文档直接 embedding，经济模式默认为该模式
          - <code>hierarchical_model</code> parent-child 模式
          - <code>qa_model</code> Q&A 模式：为分片文档生成 Q&A 对，然后对问题进行 embedding
        - <code>doc_type</code> 文档类型（选填）Type of document (optional)
          - <code>book</code> 图书
            文档记录一本书籍或出版物
          - <code>web_page</code> 网页
            网页内容的文档记录
          - <code>paper</code> 学术论文/文章
            学术论文或研究文章的记录
          - <code>social_media_post</code> 社交媒体帖子
            社交媒体上的帖子内容
          - <code>wikipedia_entry</code> 维基百科条目
            维基百科的词条内容
          - <code>personal_document</code> 个人文档
            个人相关的文档记录
          - <code>business_document</code> 商业文档
            商业相关的文档记录
          - <code>im_chat_log</code> 即时通讯记录
            即时通讯的聊天记录
          - <code>synced_from_notion</code> Notion同步文档
            从Notion同步的文档内容
          - <code>synced_from_github</code> GitHub同步文档
            从GitHub同步的文档内容
          - <code>others</code> 其他文档类型
            其他未列出的文档类型

        - <code>doc_metadata</code> 文档元数据（如提供文档类型则必填
          字段因文档类型而异

          针对图书类型 For <code>book</code>:
          - <code>title</code> 书名
            书籍的标题
          - <code>language</code> 图书语言
            书籍的语言
          - <code>author</code> 作者
            书籍的作者
          - <code>publisher</code> 出版社
            出版社的名称
          - <code>publication_date</code> 出版日期
            书籍的出版日期
          - <code>isbn</code> ISBN号码
            书籍的ISBN编号
          - <code>category</code> 图书分类
            书籍的分类类别

          针对网页类型 For <code>web_page</code>:
          - <code>title</code> 页面标题
            网页的标题
          - <code>url</code> 页面网址
            网页的URL地址
          - <code>language</code> 页面语言
            网页的语言
          - <code>publish_date</code> 发布日期
            网页的发布日期
          - <code>author/publisher</code> 作者/发布者
            网页的作者或发布者
          - <code>topic/keywords</code> 主题/关键词
            网页的主题或关键词
          - <code>description</code> 页面描述
            网页的描述信息

          请查看 [api/services/dataset_service.py](https://github.com/langgenius/dify/blob/main/api/services/dataset_service.py#L475) 了解各文档类型所需字段的详细信息。

          针对"其他"类型文档，接受任何有效的JSON对象

        - <code>doc_language</code> 在 Q&A 模式下，指定文档的语言，例如：<code>English</code>、<code>Chinese</code>

        - <code>process_rule</code> 处理规则
          - <code>mode</code> (string) 清洗、分段模式 ，automatic 自动 / custom 自定义
          - <code>rules</code> (object) 自定义规则（自动模式下，该字段为空）
            - <code>pre_processing_rules</code> (array[object]) 预处理规则
              - <code>id</code> (string) 预处理规则的唯一标识符
                - 枚举：
                  - <code>remove_extra_spaces</code> 替换连续空格、换行符、制表符
                  - <code>remove_urls_emails</code> 删除 URL、电子邮件地址
              - <code>enabled</code> (bool) 是否选中该规则，不传入文档 ID 时代表默认值
            - <code>segmentation</code> (object) 分段规则
              - <code>separator</code> 自定义分段标识符，目前仅允许设置一个分隔符。默认为 \n
              - <code>max_tokens</code> 最大长度（token）默认为 1000
            - <code>parent_mode</code> 父分段的召回模式 <code>full-doc</code> 全文召回 / <code>paragraph</code> 段落召回
            - <code>subchunk_segmentation</code> (object) 子分段规则
              - <code>separator</code> 分段标识符，目前仅允许设置一个分隔符。默认为 <code>***</code>
              - <code>max_tokens</code> 最大长度 (token) 需要校验小于父级的长度
              - <code>chunk_overlap</code> 分段重叠指的是在对数据进行分段时，段与段之间存在一定的重叠部分（选填）
      </Property>
      <Property name='file' type='multipart/form-data' key='file'>
        需要上传的文件。
      </Property>
      <PropertyInstruction>当知识库未设置任何参数的时候，首次上传需要提供以下参数，未提供则使用默认选项：</PropertyInstruction>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        检索模式
          - <code>search_method</code> (string) 检索方法
            - <code>hybrid_search</code> 混合检索
            - <code>semantic_search</code> 语义检索
            - <code>full_text_search</code> 全文检索
          - <code>reranking_enable</code> (bool) 是否开启rerank
          - <code>reranking_model</code> (object) Rerank 模型配置
            - <code>reranking_provider_name</code> (string) Rerank 模型的提供商
            - <code>reranking_model_name</code> (string) Rerank 模型的名称
          - <code>top_k</code> (int) 召回条数
          - <code>score_threshold_enabled</code> (bool)是否开启召回分数限制
          - <code>score_threshold</code> (float) 召回分数限制
      </Property>
      <Property name='embedding_model' type='string' key='embedding_model'>
        Embedding 模型名称
      </Property>
      <Property name='embedding_model_provider' type='string' key='embedding_model_provider'>
        Embedding 模型供应商
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/document/create-by-file"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'data="{"indexing_technique":"high_quality","process_rule":{"rules":{"pre_processing_rules":[{"id":"remove_extra_spaces","enabled":true},{"id":"remove_urls_emails","enabled":true}],"segmentation":{"separator":"###","max_tokens":500}},"mode":"custom"}}";type=text/plain' \\\n--form 'file=@"/path/to/file"'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/document/create-by-file' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'data="{\"name\":\"Dify\",\"indexing_technique\":\"high_quality\",\"process_rule\":{\"rules\":{\"pre_processing_rules\":[{\"id\":\"remove_extra_spaces\",\"enabled\":true},{\"id\":\"remove_urls_emails\",\"enabled\":true}],\"segmentation\":{\"separator\":\"###\",\"max_tokens\":500}},\"mode\":\"custom\"}}";type=text/plain' \
    --form 'file=@"/path/to/file"'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "Dify.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets'
  method='POST'
  title='创建空知识库'
  name='#create_empty_dataset'
/>
<Row>
  <Col>
    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        知识库名称（必填）
      </Property>
      <Property name='description' type='string' key='description'>
        知识库描述（选填）
      </Property>
      <Property name='indexing_technique' type='string' key='indexing_technique'>
        索引模式（选填，建议填写）
          - <code>high_quality</code> 高质量
          - <code>economy</code> 经济
      </Property>
      <Property name='permission' type='string' key='permission'>
        权限（选填，默认 only_me）
          - <code>only_me</code> 仅自己
          - <code>all_team_members</code> 所有团队成员
          - <code>partial_members</code> 部分团队成员
      </Property>
      <Property name='provider' type='string' key='provider'>
        Provider（选填，默认 vendor）
          - <code>vendor</code> 上传文件
          - <code>external</code> 外部知识库
      </Property>
      <Property name='external_knowledge_api_id' type='str' key='external_knowledge_api_id'>
        外部知识库 API_ID（选填）
      </Property>
      <Property name='external_knowledge_id' type='str' key='external_knowledge_id'>
        外部知识库 ID（选填）
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "name", "permission": "only_me"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "name": "name",
      "permission": "only_me"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "",
      "name": "name",
      "description": null,
      "provider": "vendor",
      "permission": "only_me",
      "data_source_type": null,
      "indexing_technique": null,
      "app_count": 0,
      "document_count": 0,
      "word_count": 0,
      "created_by": "",
      "created_at": **********,
      "updated_by": "",
      "updated_at": **********,
      "embedding_model": null,
      "embedding_model_provider": null,
      "embedding_available": null
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets'
  method='GET'
  title='知识库列表'
  name='#dataset_list'
/>
<Row>
  <Col>
    ### Query
    <Properties>
      <Property name='page' type='string' key='page'>
        页码
      </Property>
      <Property name='limit' type='string' key='limit'>
        返回条数，默认 20，范围 1-100
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets?page=1&limit=20' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets?page=1&limit=20' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [
        {
          "id": "",
          "name": "知识库名称",
          "description": "描述信息",
          "permission": "only_me",
          "data_source_type": "upload_file",
          "indexing_technique": "",
          "app_count": 2,
          "document_count": 10,
          "word_count": 1200,
          "created_by": "",
          "created_at": "",
          "updated_by": "",
          "updated_at": ""
        },
        ...
      ],
      "has_more": true,
      "limit": 20,
      "total": 50,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}'
  method='DELETE'
  title='删除知识库'
  name='#delete_dataset'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}' \
    --header 'Authorization: Bearer {api_key}'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```text {{ title: 'Response' }}
    204 No Content
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/update-by-text'
  method='POST'
  title='通过文本更新文档'
  name='#update-by-text'
/>
<Row>
  <Col>
    此接口基于已存在知识库，在此知识库的基础上通过文本更新文档

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        文档名称（选填）
      </Property>
      <Property name='text' type='string' key='text'>
        文档内容（选填）
      </Property>
      <Property name='doc_type' type='string' key='doc_type'>
        文档类型（选填）
          - <code>book</code> 图书 Book
          - <code>web_page</code> 网页 Web page
          - <code>paper</code> 学术论文/文章 Academic paper/article 
          - <code>social_media_post</code> 社交媒体帖子 Social media post
          - <code>wikipedia_entry</code> 维基百科条目 Wikipedia entry
          - <code>personal_document</code> 个人文档 Personal document
          - <code>business_document</code> 商业文档 Business document
          - <code>im_chat_log</code> 即时通讯记录 Chat log
          - <code>synced_from_notion</code> Notion同步文档 Notion document
          - <code>synced_from_github</code> GitHub同步文档 GitHub document
          - <code>others</code> 其他文档类型 Other document types
      </Property>
      <Property name='doc_metadata' type='object' key='doc_metadata'>
      
        文档元数据（如提供文档类型则必填）。字段因文档类型而异：
          
          针对图书 For <code>book</code>:
          - <code>title</code> 书名 Book title 
          - <code>language</code> 图书语言 Book language
          - <code>author</code> 作者 Book author
          - <code>publisher</code> 出版社 Publisher name
          - <code>publication_date</code> 出版日期 Publication date
          - <code>isbn</code> ISBN号码 ISBN number
          - <code>category</code> 图书分类 Book category

          针对网页 For <code>web_page</code>:
          - <code>title</code> 页面标题 Page title
          - <code>url</code> 页面网址 Page URL
          - <code>language</code> 页面语言 Page language
          - <code>publish_date</code> 发布日期 Publish date
          - <code>author/publisher</code> 作者/发布者 Author or publisher
          - <code>topic/keywords</code> 主题/关键词 Topic or keywords
          - <code>description</code> 页面描述 Page description

          请查看 [api/services/dataset_service.py](https://github.com/langgenius/dify/blob/main/api/services/dataset_service.py#L475) 了解各文档类型所需字段的详细信息。

          针对"其他"类型文档，接受任何有效的JSON对象
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        处理规则（选填）
          - <code>mode</code> (string) 清洗、分段模式 ，automatic 自动 / custom 自定义
          - <code>rules</code> (object) 自定义规则（自动模式下，该字段为空）
            - <code>pre_processing_rules</code> (array[object]) 预处理规则
              - <code>id</code> (string) 预处理规则的唯一标识符
                - 枚举：
                  - <code>remove_extra_spaces</code> 替换连续空格、换行符、制表符
                  - <code>remove_urls_emails</code> 删除 URL、电子邮件地址
              - <code>enabled</code> (bool) 是否选中该规则，不传入文档 ID 时代表默认值
            - <code>segmentation</code> (object) 分段规则
              - <code>separator</code> 自定义分段标识符，目前仅允许设置一个分隔符。默认为 \n
              - <code>max_tokens</code> 最大长度（token）默认为 1000
            - <code>parent_mode</code> 父分段的召回模式 <code>full-doc</code> 全文召回 / <code>paragraph</code> 段落召回
            - <code>subchunk_segmentation</code> (object) 子分段规则
              - <code>separator</code> 分段标识符，目前仅允许设置一个分隔符。默认为 <code>***</code>
              - <code>max_tokens</code> 最大长度 (token) 需要校验小于父级的长度
              - <code>chunk_overlap</code> 分段重叠指的是在对数据进行分段时，段与段之间存在一定的重叠部分（选填）
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/update-by-text"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-text' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"name": "name","text": "text"}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-text' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "name": "name",
        "text": "text"
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "name.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": ""
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/update-by-file'
  method='POST'
  title='通过文件更新文档'
  name='#update-by-file'
/>
<Row>
  <Col>
    此接口基于已存在知识库，在此知识库的基础上通过文件更新文档的操作。

    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='name' type='string' key='name'>
        文档名称（选填）
      </Property>
      <Property name='file' type='multipart/form-data' key='file'>
        需要上传的文件
      </Property>
      <Property name='process_rule' type='object' key='process_rule'>
        处理规则（选填）
          - <code>mode</code> (string) 清洗、分段模式 ，automatic 自动 / custom 自定义
          - <code>rules</code> (object) 自定义规则（自动模式下，该字段为空）
            - <code>pre_processing_rules</code> (array[object]) 预处理规则
              - <code>id</code> (string) 预处理规则的唯一标识符
                - 枚举：
                  - <code>remove_extra_spaces</code> 替换连续空格、换行符、制表符
                  - <code>remove_urls_emails</code> 删除 URL、电子邮件地址
              - <code>enabled</code> (bool) 是否选中该规则，不传入文档 ID 时代表默认值
            - <code>segmentation</code> (object) 分段规则
              - <code>separator</code> 自定义分段标识符，目前仅允许设置一个分隔符。默认为 \n
              - <code>max_tokens</code> 最大长度（token）默认为 1000
            - <code>parent_mode</code> 父分段的召回模式 <code>full-doc</code> 全文召回 / <code>paragraph</code> 段落召回
            - <code>subchunk_segmentation</code> (object) 子分段规则
              - <code>separator</code> 分段标识符，目前仅允许设置一个分隔符。默认为 <code>***</code>
              - <code>max_tokens</code> 最大长度 (token) 需要校验小于父级的长度
              - <code>chunk_overlap</code> 分段重叠指的是在对数据进行分段时，段与段之间存在一定的重叠部分（选填）
            - <code>doc_type</code> 文档类型（选填）Type of document (optional)
              - <code>book</code> 图书
                文档记录一本书籍或出版物
              - <code>web_page</code> 网页
                网页内容的文档记录
              - <code>paper</code> 学术论文/文章
                学术论文或研究文章的记录
              - <code>social_media_post</code> 社交媒体帖子
                社交媒体上的帖子内容
              - <code>wikipedia_entry</code> 维基百科条目
                维基百科的词条内容
              - <code>personal_document</code> 个人文档
                个人相关的文档记录
              - <code>business_document</code> 商业文档
                商业相关的文档记录
              - <code>im_chat_log</code> 即时通讯记录
                即时通讯的聊天记录
              - <code>synced_from_notion</code> Notion同步文档
                从Notion同步的文档内容
              - <code>synced_from_github</code> GitHub同步文档
                从GitHub同步的文档内容
              - <code>others</code> 其他文档类型
                其他未列出的文档类型

            - <code>doc_metadata</code> 文档元数据（如提供文档类型则必填
              字段因文档类型而异

              针对图书类型 For <code>book</code>:
              - <code>title</code> 书名
                书籍的标题
              - <code>language</code> 图书语言
                书籍的语言
              - <code>author</code> 作者
                书籍的作者
              - <code>publisher</code> 出版社
                出版社的名称
              - <code>publication_date</code> 出版日期
                书籍的出版日期
              - <code>isbn</code> ISBN号码
                书籍的ISBN编号
              - <code>category</code> 图书分类
                书籍的分类类别

              针对网页类型 For <code>web_page</code>:
              - <code>title</code> 页面标题
                网页的标题
              - <code>url</code> 页面网址
                网页的URL地址
              - <code>language</code> 页面语言
                网页的语言
              - <code>publish_date</code> 发布日期
                网页的发布日期
              - <code>author/publisher</code> 作者/发布者
                网页的作者或发布者
              - <code>topic/keywords</code> 主题/关键词
                网页的主题或关键词
              - <code>description</code> 页面描述
                网页的描述信息

              请查看 [api/services/dataset_service.py](https://github.com/langgenius/dify/blob/main/api/services/dataset_service.py#L475) 了解各文档类型所需字段的详细信息。

              针对"其他"类型文档，接受任何有效的JSON对象
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/update-by-file"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--form 'data="{"name":"Dify","indexing_technique":"high_quality","process_rule":{"rules":{"pre_processing_rules":[{"id":"remove_extra_spaces","enabled":true},{"id":"remove_urls_emails","enabled":true}],"segmentation":{"separator":"###","max_tokens":500}},"mode":"custom"}}";type=text/plain' \\\n--form 'file=@"/path/to/file"'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/update-by-file' \
    --header 'Authorization: Bearer {api_key}' \
    --form 'data="{\"name\":\"Dify\",\"indexing_technique\":\"high_quality\",\"process_rule\":{\"rules\":{\"pre_processing_rules\":[{\"id\":\"remove_extra_spaces\",\"enabled\":true},{\"id\":\"remove_urls_emails\",\"enabled\":true}],\"segmentation\":{\"separator\":\"###\",\"max_tokens\":500}},\"mode\":\"custom\"}}";type=text/plain' \
    --form 'file=@"/path/to/file"'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "document": {
        "id": "",
        "position": 1,
        "data_source_type": "upload_file",
        "data_source_info": {
          "upload_file_id": ""
        },
        "dataset_process_rule_id": "",
        "name": "Dify.txt",
        "created_from": "api",
        "created_by": "",
        "created_at": 1695308667,
        "tokens": 0,
        "indexing_status": "waiting",
        "error": null,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "archived": false,
        "display_status": "queuing",
        "word_count": 0,
        "hit_count": 0,
        "doc_form": "text_model"
      },
      "batch": "20230921150427533684"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{batch}/indexing-status'
  method='GET'
  title='获取文档嵌入状态（进度）'
  name='#indexing_status'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='batch' type='string' key='batch'>
        上传文档的批次号
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{batch}/indexing-status"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{batch}/indexing-status' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{batch}/indexing-status' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data":[{
        "id": "",
        "indexing_status": "indexing",
        "processing_started_at": 1681623462.0,
        "parsing_completed_at": 1681623462.0,
        "cleaning_completed_at": 1681623462.0,
        "splitting_completed_at": 1681623462.0,
        "completed_at": null,
        "paused_at": null,
        "error": null,
        "stopped_at": null,
        "completed_segments": 24,
        "total_segments": 100
      }]
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}'
  method='DELETE'
  title='删除文档'
  name='#delete_document'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents'
  method='GET'
  title='知识库文档列表'
  name='#dataset_document_list'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
    </Properties>

    ### Query
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        搜索关键词，可选，目前仅搜索文档名称
      </Property>
      <Property name='page' type='string' key='page'>
        页码，可选
      </Property>
      <Property name='limit' type='string' key='limit'>
        返回条数，可选，默认 20，范围 1-100
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents' \\\n--header 'Authorization: Bearer {api_key}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents' \
    --header 'Authorization: Bearer {api_key}' \
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [
        {
          "id": "",
          "position": 1,
          "data_source_type": "file_upload",
          "data_source_info": null,
          "dataset_process_rule_id": null,
          "name": "dify",
          "created_from": "",
          "created_by": "",
          "created_at": 1681623639,
          "tokens": 0,
          "indexing_status": "waiting",
          "error": null,
          "enabled": true,
          "disabled_at": null,
          "disabled_by": null,
          "archived": false
        },
      ],
      "has_more": false,
      "limit": 20,
      "total": 9,
      "page": 1
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments'
  method='POST'
  title='新增分段'
  name='#create_new_segment'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='segments' type='object list' key='segments'>
        - <code>content</code> (text) 文本内容/问题内容，必填
        - <code>answer</code> (text) 答案内容，非必填，如果知识库的模式为 Q&A 模式则传值
        - <code>keywords</code> (list) 关键字，非必填
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{"segments": [{"content": "1","answer": "1","keywords": ["a"]}]}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "segments": [
        {
          "content": "1",
          "answer": "1",
          "keywords": ["a"]
        }
      ]
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
            "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "doc_form": "text_model"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments'
  method='GET'
  title='查询文档分段'
  name='#get_segment'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
    </Properties>

     ### Query
    <Properties>
      <Property name='keyword' type='string' key='keyword'>
        搜索关键词，可选
      </Property>
      <Property name='status' type='string' key='status'>
        搜索状态，completed
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/segments"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
            "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "doc_form": "text_model"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}'
  method='DELETE'
  title='删除文档分段'
  name='#delete_segment'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        文档分段ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="DELETE"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
      targetCode={`curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request DELETE '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "result": "success"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}'
  method='POST'
  title='更新文档分段'
  name='#update_segment'
/>
<Row>
  <Col>
    ### POST
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
      <Property name='segment_id' type='string' key='segment_id'>
        文档分段ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='segment' type='object' key='segment'>
        - <code>content</code> (text) 文本内容/问题内容，必填
        - <code>answer</code> (text) 答案内容，非必填，如果知识库的模式为 Q&A 模式则传值
        - <code>keywords</code> (list) 关键字，非必填
        - <code>enabled</code> (bool) false/true，非必填
        - <code>regenerate_child_chunks</code> (bool) 是否重新生成子分段，非必填
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'\\\n--data-raw '{\"segment\": {\"content\": \"1\",\"answer\": \"1\", \"keywords\": [\"a\"], \"enabled\": false}}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
      "segment": {
          "content": "1",
          "answer": "1",
          "keywords": ["a"],
          "enabled": false
      }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "data": [{
        "id": "",
        "position": 1,
        "document_id": "",
        "content": "1",
        "answer": "1",
        "word_count": 25,
        "tokens": 0,
        "keywords": [
            "a"
        ],
        "index_node_id": "",
        "index_node_hash": "",
        "hit_count": 0,
        "enabled": true,
        "disabled_at": null,
        "disabled_by": null,
        "status": "completed",
        "created_by": "",
        "created_at": 1695312007,
        "indexing_at": 1695312007,
        "completed_at": 1695312007,
        "error": null,
        "stopped_at": null
      }],
      "doc_form": "text_model"
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/documents/{document_id}/upload-file'
  method='GET'
  title='获取上传文件'
  name='#get_upload_file'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
      <Property name='document_id' type='string' key='document_id'>
        文档 ID
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="GET"
      label="/datasets/{dataset_id}/documents/{document_id}/upload-file"
      targetCode={`curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/upload-file' \\\n--header 'Authorization: Bearer {api_key}' \\\n--header 'Content-Type: application/json'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request GET '${props.apiBaseUrl}/datasets/{dataset_id}/documents/{document_id}/upload-file' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "id": "file_id",
      "name": "file_name",
      "size": 1024,
      "extension": "txt",
      "url": "preview_url",
      "download_url": "download_url",
      "mime_type": "text/plain",
      "created_by": "user_id",
      "created_at": 1728734540,
    }
    ```
    </CodeGroup>
  </Col>
</Row>

<hr className='ml-0 mr-0' />

<Heading
  url='/datasets/{dataset_id}/retrieve'
  method='POST'
  title='检索知识库'
  name='#dataset_retrieval'
/>
<Row>
  <Col>
    ### Path
    <Properties>
      <Property name='dataset_id' type='string' key='dataset_id'>
        知识库 ID
      </Property>
    </Properties>

    ### Request Body
    <Properties>
      <Property name='query' type='string' key='query'>
        检索关键词
      </Property>
      <Property name='retrieval_model' type='object' key='retrieval_model'>
        检索参数（选填，如不填，按照默认方式召回）
        - <code>search_method</code> (text) 检索方法：以下三个关键字之一，必填
          - <code>keyword_search</code> 关键字检索
          - <code>semantic_search</code> 语义检索
          - <code>full_text_search</code> 全文检索
          - <code>hybrid_search</code> 混合检索
        - <code>reranking_enable</code> (bool) 是否启用 Reranking，非必填，如果检索模式为 semantic_search 模式或者 hybrid_search 则传值
        - <code>reranking_mode</code> (object) Rerank 模型配置，非必填，如果启用了 reranking 则传值
            - <code>reranking_provider_name</code> (string) Rerank 模型提供商
            - <code>reranking_model_name</code> (string) Rerank 模型名称
        - <code>weights</code> (float) 混合检索模式下语意检索的权重设置
        - <code>top_k</code> (integer) 返回结果数量，非必填
        - <code>score_threshold_enabled</code> (bool) 是否开启 score 阈值
        - <code>score_threshold</code> (float) Score 阈值
      </Property>
      <Property name='external_retrieval_model' type='object' key='external_retrieval_model'>
          未启用字段
      </Property>
    </Properties>
  </Col>
  <Col sticky>
    <CodeGroup
      title="Request"
      tag="POST"
      label="/datasets/{dataset_id}/retrieve"
      targetCode={`curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/retrieve' \\\n--header 'Authorization: Bearer {api_key}'\\\n--header 'Content-Type: application/json'\\\n--data-raw '{
    "query": "test",
    "retrieval_model": {
        "search_method": "keyword_search",
        "reranking_enable": false,
        "reranking_mode": null,
        "reranking_model": {
            "reranking_provider_name": "",
            "reranking_model_name": ""
        },
        "weights": null,
        "top_k": 1,
        "score_threshold_enabled": false,
        "score_threshold": null
    }
}'`}
    >
    ```bash {{ title: 'cURL' }}
    curl --location --request POST '${props.apiBaseUrl}/datasets/{dataset_id}/retrieve' \
    --header 'Authorization: Bearer {api_key}' \
    --header 'Content-Type: application/json' \
    --data-raw '{
        "query": "test",
        "retrieval_model": {
            "search_method": "keyword_search",
            "reranking_enable": false,
            "reranking_mode": null,
            "reranking_model": {
                "reranking_provider_name": "",
                "reranking_model_name": ""
            },
            "weights": null,
            "top_k": 2,
            "score_threshold_enabled": false,
            "score_threshold": null
        }
    }'
    ```
    </CodeGroup>
    <CodeGroup title="Response">
    ```json {{ title: 'Response' }}
    {
      "query": {
        "content": "test"
      },
      "records": [
        {
          "segment": {
            "id": "7fa6f24f-8679-48b3-bc9d-bdf28d73f218",
            "position": 1,
            "document_id": "a8c6c36f-9f5d-4d7a-8472-f5d7b75d71d2",
            "content": "Operation guide",
            "answer": null,
            "word_count": 847,
            "tokens": 280,
            "keywords": [
              "install",
              "java",
              "base",
              "scripts",
              "jdk",
              "manual",
              "internal",
              "opens",
              "add",
              "vmoptions"
            ],
            "index_node_id": "39dd8443-d960-45a8-bb46-7275ad7fbc8e",
            "index_node_hash": "0189157697b3c6a418ccf8264a09699f25858975578f3467c76d6bfc94df1d73",
            "hit_count": 0,
            "enabled": true,
            "disabled_at": null,
            "disabled_by": null,
            "status": "completed",
            "created_by": "dbcb1ab5-90c8-41a7-8b78-73b235eb6f6f",
            "created_at": 1728734540,
            "indexing_at": 1728734552,
            "completed_at": 1728734584,
            "error": null,
            "stopped_at": null,
            "document": {
              "id": "a8c6c36f-9f5d-4d7a-8472-f5d7b75d71d2",
              "data_source_type": "upload_file",
              "name": "readme.txt",
              "doc_type": null
            }
          },
          "score": 3.730463140527718e-05,
          "tsne_position": null
        }
      ]
    }
    ```
    </CodeGroup>
  </Col>
</Row>


<hr className='ml-0 mr-0' />

<Row>
  <Col>
    ### 错误信息
    <Properties>
      <Property name='code' type='string' key='code'>
        返回的错误代码
      </Property>
    </Properties>
    <Properties>
      <Property name='status' type='number' key='status'>
        返回的错误状态
      </Property>
    </Properties>
    <Properties>
      <Property name='message' type='string' key='message'>
        返回的错误信息
      </Property>
    </Properties>
  </Col>
  <Col>
    <CodeGroup title="Example">
    ```json {{ title: 'Response' }}
      {
        "code": "no_file_uploaded",
        "message": "Please upload your file.",
        "status": 400
      }
    ```
    </CodeGroup>
  </Col>
</Row>
<table className="max-w-auto border-collapse border border-slate-400" style={{ maxWidth: 'none', width: 'auto' }}>
  <thead style={{ background: '#f9fafc' }}>
    <tr>
      <th className="p-2 border border-slate-300">code</th>
      <th className="p-2 border border-slate-300">status</th>
      <th className="p-2 border border-slate-300">message</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td className="p-2 border border-slate-300">no_file_uploaded</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Please upload your file.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">too_many_files</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Only one file is allowed.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">file_too_large</td>
      <td className="p-2 border border-slate-300">413</td>
      <td className="p-2 border border-slate-300">File size exceeded.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">unsupported_file_type</td>
      <td className="p-2 border border-slate-300">415</td>
      <td className="p-2 border border-slate-300">File type not allowed.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">high_quality_dataset_only</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Current operation only supports 'high-quality' datasets.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">dataset_not_initialized</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The dataset is still being initialized or indexing. Please wait a moment.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">archived_document_immutable</td>
      <td className="p-2 border border-slate-300">403</td>
      <td className="p-2 border border-slate-300">The archived document is not editable.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">dataset_name_duplicate</td>
      <td className="p-2 border border-slate-300">409</td>
      <td className="p-2 border border-slate-300">The dataset name already exists. Please modify your dataset name.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">invalid_action</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">Invalid action.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">document_already_finished</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The document has been processed. Please refresh the page or go to the document details.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">document_indexing</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The document is being processed and cannot be edited.</td>
    </tr>
    <tr>
      <td className="p-2 border border-slate-300">invalid_metadata</td>
      <td className="p-2 border border-slate-300">400</td>
      <td className="p-2 border border-slate-300">The metadata content is incorrect. Please check and verify.</td>
    </tr>
  </tbody>
</table>
<div className="pb-4" />

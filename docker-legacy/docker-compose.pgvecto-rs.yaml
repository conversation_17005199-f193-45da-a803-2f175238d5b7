services:
  # The pgvecto—rs database.
  pgvecto-rs:
    image: tensorchord/pgvecto-rs:pg16-v0.2.0
    restart: always
    environment:
      PGUSER: postgres
      # The password for the default postgres user.
      POSTGRES_PASSWORD: difyai123456
      # The name of the default postgres database.
      POSTGRES_DB: dify
      # postgres data directory
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ./volumes/pgvectors/data:/var/lib/postgresql/data
    # uncomment to expose db(postgresql) port to host
    ports:
      - "5431:5432"
    healthcheck:
      test: [ "CMD", "pg_isready" ]
      interval: 1s
      timeout: 3s
      retries: 30

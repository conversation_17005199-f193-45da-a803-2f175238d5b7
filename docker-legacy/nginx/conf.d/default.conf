server {
    listen 80;
    server_name _;

    location /console/api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /api {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /v1 {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location /files {
      proxy_pass http://api:5001;
      include proxy.conf;
    }

    location / {
      proxy_pass http://web:3000;
      include proxy.conf;
    }

    # If you want to support HTTPS, please uncomment the code snippet below
    #listen 443 ssl;
    #ssl_certificate ./../ssl/your_cert_file.cer;
    #ssl_certificate_key ./../ssl/your_cert_key.key;
    #ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    #ssl_prefer_server_ciphers on;
    #ssl_session_cache shared:SSL:10m;
    #ssl_session_timeout 10m;
}

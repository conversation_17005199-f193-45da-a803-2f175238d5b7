services:
  # Qdrant vector store.
  pgvector:
    image: pgvector/pgvector:pg16
    restart: always
    environment:
      PGUSER: postgres
      # The password for the default postgres user.
      POSTGRES_PASSWORD: difyai123456
      # The name of the default postgres database.
      POSTGRES_DB: dify
      # postgres data directory
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ./volumes/pgvector/data:/var/lib/postgresql/data
    # uncomment to expose db(postgresql) port to host
    ports:
      - "5433:5432"
    healthcheck:
      test: [ "CMD", "pg_isready" ]
      interval: 1s
      timeout: 3s
      retries: 30

provider: wenxin
label:
  en_US: <PERSON><PERSON><PERSON>
  zh_Hans: 文心一言
icon_small:
  en_US: icon_s_en.png
  zh_Hans: icon_s_en.png
icon_large:
  en_US: icon_l_en.png
  zh_Hans: icon_l_zh.png
background: "#E8F5FE"
help:
  title:
    en_US: Get your API Key from WenXin
    zh_Hans: 从文心一言获取您的 API Key
  url:
    en_US: https://cloud.baidu.com/wenxin.html
supported_model_types:
  - llm
  - text-embedding
  - rerank
configurate_methods:
  - predefined-model
provider_credential_schema:
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
    - variable: secret_key
      label:
        en_US: Secret Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 Secret Key
        en_US: Enter your Secret Key

﻿provider: fishaudio
label:
  en_US: Fish Audio
description:
  en_US: Models provided by Fish Audio, currently only support TTS.
  zh_Hans: Fish Audio 提供的模型，目前仅支持 TTS。
icon_small:
  en_US: fishaudio_s_en.svg
icon_large:
  en_US: fishaudio_l_en.svg
background: "#E5E7EB"
help:
  title:
    en_US: Get your API key from Fish Audio
    zh_Hans: 从 Fish Audio 获取你的 API Key
  url:
    en_US: https://fish.audio/go-api/
supported_model_types:
  - tts
configurate_methods:
  - predefined-model
provider_credential_schema:
  credential_form_schemas:
    - variable: api_key
      label:
        en_US: API Key
      type: secret-input
      required: true
      placeholder:
        zh_Hans: 在此输入您的 API Key
        en_US: Enter your API Key
    - variable: api_base
      label:
        en_US: API URL
      type: text-input
      required: false
      default: https://api.fish.audio
      placeholder:
        en_US: Enter your API URL
        zh_Hans: 在此输入您的 API URL
    - variable: use_public_models
      label:
        en_US: Use Public Models
      type: select
      required: false
      default: "false"
      placeholder:
        en_US: Toggle to use public models
        zh_Hans: 切换以使用公共模型
      options:
        - value: "true"
          label:
            en_US: Allow Public Models
            zh_Hans: 使用公共模型
        - value: "false"
          label:
            en_US: Private Models Only
            zh_Hans: 仅使用私有模型
    - variable: latency
      label:
        en_US: Latency
      type: select
      required: false
      default: "normal"
      placeholder:
        en_US: Toggle to choice latency
        zh_Hans: 切换以调整延迟
      options:
        - value: "balanced"
          label:
            en_US: Low (may affect quality)
            zh_Hans: 低延迟 (可能降低质量)
        - value: "normal"
          label:
            en_US: Normal
            zh_Hans: 标准

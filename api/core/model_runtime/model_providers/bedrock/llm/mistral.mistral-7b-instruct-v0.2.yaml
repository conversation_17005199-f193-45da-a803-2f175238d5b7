model: mistral.mistral-7b-instruct-v0:2
label:
  en_US: Mistral 7B Instruct
model_type: llm
model_properties:
  mode: completion
  context_size: 32000
parameter_rules:
  - name: temperature
    use_template: temperature
    required: false
    default: 0.5
  - name: top_p
    use_template: top_p
    required: false
    default: 0.9
  - name: top_k
    use_template: top_k
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
    default: 50
    max: 200
  - name: max_tokens
    use_template: max_tokens
    required: true
    default: 512
    min: 1
    max: 8192
pricing:
  input: '0.00015'
  output: '0.0002'
  unit: '0.00001'
  currency: USD

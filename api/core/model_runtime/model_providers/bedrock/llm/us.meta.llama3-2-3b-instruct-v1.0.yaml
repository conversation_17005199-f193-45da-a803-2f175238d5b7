model: us.meta.llama3-2-3b-instruct-v1:0
label:
  en_US: US Meta Llama 3.2 3B Instruct
model_type: llm
model_properties:
  mode: completion
  context_size: 128000
parameter_rules:
  - name: temperature
    use_template: temperature
    default: 0.5
    min: 0.0
    max: 1
  - name: top_p
    use_template: top_p
  - name: max_gen_len
    use_template: max_tokens
    required: true
    default: 512
    min: 1
    max: 2048
pricing:
  input: '0.00015'
  output: '0.00015'
  unit: '0.001'
  currency: USD

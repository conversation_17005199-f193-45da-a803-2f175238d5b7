model: amazon.nova-micro-v1:0
label:
  en_US: Nova Micro V1
model_type: llm
features:
  - agent-thought
  - tool-call
  - stream-tool-call
model_properties:
  mode: chat
  context_size: 128000
parameter_rules:
  - name: max_new_tokens
    use_template: max_tokens
    required: true
    default: 2048
    min: 1
    max: 5000
  - name: temperature
    use_template: temperature
    required: false
    type: float
    default: 1
    min: 0.0
    max: 1.0
    help:
      zh_<PERSON>: 生成内容的随机性。
      en_US: The amount of randomness injected into the response.
  - name: top_p
    required: false
    type: float
    default: 0.999
    min: 0.000
    max: 1.000
    help:
      zh_Hans: 在核采样中，Anthropic Claude 按概率递减顺序计算每个后续标记的所有选项的累积分布，并在达到 top_p 指定的特定概率时将其切断。您应该更改温度或top_p，但不能同时更改两者。
      en_US: In nucleus sampling, Anthropic Claude computes the cumulative distribution over all the options for each subsequent token in decreasing probability order and cuts it off once it reaches a particular probability specified by top_p. You should alter either temperature or top_p, but not both.
  - name: top_k
    required: false
    type: int
    default: 0
    min: 0
    # tip docs from aws has error, max value is 500
    max: 500
    help:
      zh_Hans: 对于每个后续标记，仅从前 K 个选项中进行采样。使用 top_k 删除长尾低概率响应。
      en_US: Only sample from the top K options for each subsequent token. Use top_k to remove long tail low probability responses.
pricing:
  input: '0.000035'
  output: '0.00014'
  unit: '0.001'
  currency: USD

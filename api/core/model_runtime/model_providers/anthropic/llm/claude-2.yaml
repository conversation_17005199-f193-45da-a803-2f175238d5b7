model: claude-2
label:
  en_US: claude-2
model_type: llm
features:
  - agent-thought
model_properties:
  mode: chat
  context_size: 100000
parameter_rules:
  - name: temperature
    use_template: temperature
  - name: top_p
    use_template: top_p
  - name: top_k
    label:
      zh_Hans: 取样数量
      en_US: Top k
    type: int
    help:
      zh_Hans: 仅从每个后续标记的前 K 个选项中采样。
      en_US: Only sample from the top K options for each subsequent token.
    required: false
  - name: max_tokens_to_sample
    use_template: max_tokens
    required: true
    default: 4096
    min: 1
    max: 4096
  - name: response_format
    use_template: response_format
pricing:
  input: '8.00'
  output: '24.00'
  unit: '0.000001'
  currency: USD
deprecated: true

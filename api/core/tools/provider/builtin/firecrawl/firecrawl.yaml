identity:
  author: <PERSON> Tu
  name: firecrawl
  label:
    en_US: Firecrawl
    zh_CN: Firecrawl
  description:
    en_US: Firecrawl API integration for web crawling and scraping.
    zh_Hans: Firecrawl API 集成，用于网页爬取和数据抓取。
  icon: icon.svg
  tags:
    - search
    - utilities
credentials_for_provider:
  firecrawl_api_key:
    type: secret-input
    required: true
    label:
      en_US: Firecrawl API Key
      zh_Hans: Firecrawl API 密钥
    placeholder:
      en_US: Please input your Firecrawl API key
      zh_Hans: 请输入您的 Firecrawl API 密钥，如果是自托管版本，可以随意填写密钥
    help:
      en_US: Get your Firecrawl API key from your Firecrawl account settings.If you are using a self-hosted version, you may enter any key at your convenience.
      zh_Hans: 从您的 Firecrawl 账户设置中获取 Firecrawl API 密钥。如果是自托管版本，可以随意填写密钥。
    url: https://www.firecrawl.dev/account
  base_url:
    type: text-input
    required: false
    label:
      en_US: Firecrawl server's Base URL
      zh_Hans: Firecrawl服务器的API URL
    placeholder:
      en_US: https://api.firecrawl.dev

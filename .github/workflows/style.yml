name: Style check

on:
  pull_request:
    branches:
      - main

concurrency:
  group: style-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  python-style:
    name: Python Style
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: |
            api/**
            .github/workflows/style.yml

      - name: Setup Poetry and Python
        if: steps.changed-files.outputs.any_changed == 'true'
        uses: ./.github/actions/setup-poetry

      - name: Install dependencies
        if: steps.changed-files.outputs.any_changed == 'true'
        run: poetry install -C api --only lint

      - name: Ruff check
        if: steps.changed-files.outputs.any_changed == 'true'
        run: |
          poetry run -C api ruff --version
          poetry run -C api ruff check ./
          poetry run -C api ruff format --check ./

      - name: Dotenv check
        if: steps.changed-files.outputs.any_changed == 'true'
        run: poetry run -P api dotenv-linter ./api/.env.example ./web/.env.example

      - name: Lint hints
        if: failure()
        run: echo "Please run 'dev/reformat' to fix the fixable linting errors."

  web-style:
    name: Web Style
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./web

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: web/**

      - name: Setup NodeJS
        uses: actions/setup-node@v4
        if: steps.changed-files.outputs.any_changed == 'true'
        with:
          node-version: 20
          cache: yarn
          cache-dependency-path: ./web/package.json

      - name: Web dependencies
        if: steps.changed-files.outputs.any_changed == 'true'
        run: yarn install --frozen-lockfile

      - name: Web style check
        if: steps.changed-files.outputs.any_changed == 'true'
        run: yarn run lint

  docker-compose-template:
    name: Docker Compose Template
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: |
            docker/generate_docker_compose
            docker/.env.example
            docker/docker-compose-template.yaml
            docker/docker-compose.yaml

      - name: Generate Docker Compose
        if: steps.changed-files.outputs.any_changed == 'true'
        run: |
          cd docker
          ./generate_docker_compose

      - name: Check for changes
        if: steps.changed-files.outputs.any_changed == 'true'
        run: git diff --exit-code

  superlinter:
    name: SuperLinter
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: |
            **.sh
            **.yaml
            **.yml
            **Dockerfile
            dev/**

      - name: Super-linter
        uses: super-linter/super-linter/slim@v7
        if: steps.changed-files.outputs.any_changed == 'true'
        env:
          BASH_SEVERITY: warning
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          IGNORE_GENERATED_FILES: true
          IGNORE_GITIGNORED_FILES: true
          VALIDATE_BASH: true
          VALIDATE_BASH_EXEC: true
          # FIXME: temporarily disabled until api-docker.yaml's run script is fixed for shellcheck
          # VALIDATE_GITHUB_ACTIONS: true
          VALIDATE_DOCKERFILE_HADOLINT: true
          VALIDATE_XML: true
          VALIDATE_YAML: true

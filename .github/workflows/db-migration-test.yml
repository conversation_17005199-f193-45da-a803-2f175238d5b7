name: DB Migration Test

on:
  pull_request:
    branches:
      - main
    paths:
      - api/migrations/**
      - .github/workflows/db-migration-test.yml

concurrency:
  group: db-migration-test-${{ github.ref }}
  cancel-in-progress: true

jobs:
  db-migration-test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Poetry and Python
        uses: ./.github/actions/setup-poetry
        with:
          poetry-lockfile: api/poetry.lock

      - name: Install dependencies
        run: poetry install -C api

      - name: Prepare middleware env
        run: |
          cd docker
          cp middleware.env.example middleware.env

      - name: Set up Middlewares
        uses: hoverkraft-tech/compose-action@v2.0.2
        with:
          compose-file: |
            docker/docker-compose.middleware.yaml
          services: |
            db
            redis

      - name: Prepare configs
        run: |
          cd api
          cp .env.example .env

      - name: Run DB Migration
        env:
          DEBUG: true
        run: |
          cd api
          poetry run python -m flask upgrade-db
